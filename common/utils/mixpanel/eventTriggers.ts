import { Dict } from 'mixpanel-browser';
import { actions } from '.';
import {
  MixpanelEventName, CustomEventProps,
} from './types';

export type { Dict };
export { MixpanelEventName };

export const useMixpanelEvent = () => {


  // useEffect(() => {
  //   if (authenticated) {
  //     Mixpanel.identify(address as string);
  //   }
  // }, [authenticated, address])

  const mixpanelEvent = ({
    mixpanelProps = {}, eventName,
  }: CustomEventProps) => {
    try {
      actions.track(eventName, mixpanelProps);
    } catch (err) {
      console.error('error', `Mixpanel tracking error: ${err}`);
    }
  };
  return {
    mixpanelEvent,
  }
}
